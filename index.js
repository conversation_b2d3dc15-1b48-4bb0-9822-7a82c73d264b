const express = require('express')
const { createServer } = require('http')
const { Server } = require('socket.io')
const cors = require('cors')
const hpp = require('hpp')
const { SpeechClient } = require('@google-cloud/speech')
const config = require('./modules/config')
const { createAIService, generateAIReply } = require('./modules/AIService')
const { generateSpeech } = require('./modules/voiceService')
const { customLog } = require('./modules/logger')
const { cleanTextForSpeech } = require('./modules/textProcessor')
const { setSecurityHeaders, validateApiKey, configureSocketSecurity, configureCors } = require('./modules/security')
const cookieParser = require('cookie-parser')
const csurf = require('csurf')
const csrfProtection = csurf({ cookie: true })

require('better-logging')(console)

const app = express()
app.disable('x-powered-by')
const httpServer = createServer(app)

setSecurityHeaders(app)

app.use(hpp())
app.use(express.json())
configureCors(app)
app.use(cookieParser())
app.use(csrfProtection)

app.use('/api', validateApiKey)

const io = new Server(httpServer, {
  cors: {
    origin:
      process.env.NODE_ENV === 'production'
        ? [process.env.FRONTEND_DEV_URL, process.env.FRONTEND_PRO_URL]
        : ['http://localhost:3000', 'http://localhost:5173'],
    methods: ['GET', 'POST'],
    credentials: true
  }
})
configureSocketSecurity(io)

createAIService()
const client = new SpeechClient()

io.on('connection', (socket) => {
  customLog('Client connected:', socket.id)

  let recognizeStream = null
  let conversationHistory = []
  let isMuted = false
  let isStreamActive = true
  let inactivityTimeout = null

  const startRecognitionStream = () => {
    if (recognizeStream && !recognizeStream.destroyed) {
      recognizeStream.end()
    }

    customLog('Starting new recognition stream')
    isStreamActive = true

    return client
      .streamingRecognize(config.SPEECH_TO_TEXT_CONFIG)
      .on('error', (error) => {
        customLog('Error transcribing audio:', error)
        isStreamActive = false

        if (recognizeStream) {
          recognizeStream.end()
          recognizeStream = null
        }
      })
      .on('data', async (data) => {
        if (data.results[0] && data.results[0].alternatives[0]) {
          resetInactivityTimer()

          let transcription = data.results[0].alternatives[0].transcript.trim()
          socket.emit('transcription', transcription)

          if (data.results[0].isFinal && transcription && transcription !== '') {
            socket.emit('loading')
            if (transcription.toLowerCase() === 'ok aura') {
              socket.emit('reply', { text: '[OK AURA]', audioUrl: null })
            } else {
              if (transcription.toLowerCase().startsWith('ok aura')) {
                transcription = transcription.slice(7).trim()
              }

              if (transcription.length > config.MAX_USER_INPUT_LENGTH) {
                customLog(`Warning: Truncating long user input from ${transcription.length} to ${config.MAX_USER_INPUT_LENGTH} characters`)
                transcription = transcription.substring(0, config.MAX_USER_INPUT_LENGTH)
              }

              conversationHistory.push({ user: true, content: transcription })
              customLog(`User: ${transcription}`)
              let reply = await generateAIReply(conversationHistory)
              customLog(`AI: ${reply}`)
              conversationHistory.push({ user: false, content: reply })
              if (
                reply.startsWith('[JSON]') ||
                reply.startsWith('[NONE]') ||
                reply.startsWith('[COMMAND]') ||
                reply.startsWith('[PAUSE]') ||
                reply.startsWith('[EXIT]')
              ) {
                socket.emit('reply', { text: reply, audioUrl: null })
              } else {
                const audioUrl = !isMuted
                  ? `data:audio/mpeg;base64,${(await generateSpeech(cleanTextForSpeech(reply))).toString('base64')}`
                  : null
                socket.emit('reply', { text: reply, audioUrl })
              }
            }
          }
        }
      })
  }

  const resetInactivityTimer = () => {
    if (inactivityTimeout) {
      clearTimeout(inactivityTimeout)
    }

    inactivityTimeout = setTimeout(() => {
      if (recognizeStream && !recognizeStream.destroyed) {
        customLog('Closing stream due to inactivity')
        recognizeStream.end()
        recognizeStream = null
        isStreamActive = false
      }
    }, 60000) // 1 minute
  }

  socket.on('audio', (audioBuffer) => {
    if (!recognizeStream || !isStreamActive) {
      recognizeStream = startRecognitionStream()
    }

    const buffer = Buffer.from(new Uint8Array(audioBuffer))

    try {
      recognizeStream.write(buffer)
      resetInactivityTimer()
    } catch (error) {
      customLog('Error writing to stream:', error)
      recognizeStream = startRecognitionStream()
      recognizeStream.write(buffer)
    }
  })

  socket.on('disconnect', () => {
    customLog('Client disconnected:', socket.id)
    if (recognizeStream) {
      recognizeStream.end()
      recognizeStream = null
    }
    if (inactivityTimeout) {
      clearTimeout(inactivityTimeout)
    }
  })

  socket.on('mute-sound', () => {
    isMuted = true
    customLog(`Sound muted for client: ${socket.id}`)
  })

  socket.on('unmute-sound', () => {
    isMuted = false
    customLog(`Sound unmuted for client: ${socket.id}`)
  })

  recognizeStream = startRecognitionStream()
  resetInactivityTimer()
})

httpServer.listen(config.PORT, () => {
  customLog(`Server running on port ${config.PORT}`)
})
