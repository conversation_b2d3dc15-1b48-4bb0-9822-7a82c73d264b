const axios = require('axios')

const axiosInstance = axios.create({
  baseURL: process.env.AUDIO_BACKEND_BASE_URL,
  headers: {
    Authorization: process.env.AUDIO_BACKEND_API_KEY
  }
})

const generateSpeech = async (text) => {
  try {
    const response = await axiosInstance.post(
      '/t2s',
      {
        input_text: text.replaceAll('Movistar+', 'Movistar Plus'),
        voice_params: {
          voice_id: 'Ximena'
        },
        output_format: 'mp3'
      },
      {
        responseType: 'arraybuffer'
      }
    )

    return Buffer.from(response.data, 'binary')
  } catch (error) {
    console.error('Error generating speech with Speech Server:', error)
    throw error
  }
}

module.exports = { generateSpeech }
