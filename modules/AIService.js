const { VertexAI } = require('@google-cloud/vertexai')
const config = require('./config')

let model

const createAIService = () => {
  const vertexAI = new VertexAI({
    project: 'sandbox-innovacion-3',
    location: 'europe-southwest1'
  })
  model = vertexAI.getGenerativeModel({
    model: 'gemini-2.0-flash-001',
    systemInstruction: {
      role: 'system',
      parts: [{ text: config.AI_PROMPT }]
    }
  })
}

const generateAIReply = async (conversationHistory) => {
  const formattedHistory = conversationHistory.map((turn) => ({
    role: turn.user ? 'user' : 'model',
    parts: [{ text: turn.content }]
  }))

  try {
    const result = await model.generateContent({
      contents: formattedHistory
    })
    return result.response.candidates[0].content.parts[0].text
  } catch (error) {
    console.error('Error generating AI reply:', error)
  }
}

module.exports = {
  createAIService,
  generateAIReply
}
